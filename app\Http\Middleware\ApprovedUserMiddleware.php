<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class ApprovedUserMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $user = Auth::user();

        if (!$user->canLogin()) {
            Auth::logout();

            if ($user->isPending()) {
                return redirect()->route('login')->withErrors([
                    'email' => 'Votre compte est en attente d\'approbation par un administrateur.',
                ]);
            } elseif ($user->isRejected()) {
                return redirect()->route('login')->withErrors([
                    'email' => 'Votre compte a été rejeté. Raison: ' . $user->rejection_reason,
                ]);
            } elseif ($user->isSuspended()) {
                return redirect()->route('login')->withErrors([
                    'email' => 'Votre compte a été suspendu.',
                ]);
            }
        }

        return $next($request);
    }
}

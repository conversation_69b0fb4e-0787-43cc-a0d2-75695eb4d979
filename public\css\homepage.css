/* Homepage Specific Styles */
/* Base styles from carryway-modern.css */

/* CSS Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern Color Palette - Gray Scale */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Accent Colors */
    --blue-100: #dbeafe;
    --blue-600: #2563eb;
    --green-100: #dcfce7;
    --green-500: #22c55e;
    --green-600: #16a34a;
    --purple-100: #f3e8ff;
    --purple-600: #9333ea;

    /* White */
    --white: #ffffff;

    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;

    /* Spacing */
    --container-max-width: 1280px;
    --section-padding-y: 4rem;
    --section-padding-y-lg: 6rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--white);
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    color: var(--gray-900);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;
    font-size: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Icons */
.icon {
    width: 1.25rem;
    height: 1.25rem;
}

.icon-sm {
    width: 1rem;
    height: 1rem;
}

.icon-lg {
    width: 2rem;
    height: 2rem;
}

/* Spacing utilities */
.mr-2 { margin-right: 0.5rem; }
.ml-2 { margin-left: 0.5rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

/* Grid utilities */
.grid { display: grid; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* Hero Section */
.hero-content {
    display: grid;
    gap: 3rem;
    align-items: center;
    padding: 2rem 0;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: var(--blue-100);
    color: var(--blue-600);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    line-height: 1.1;
    color: var(--gray-900);
    margin: 1rem 0;
}

.hero-subtitle {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.125rem;
    color: var(--gray-600);
    line-height: 1.7;
    margin: 1rem 0;
}

.hero-actions {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin: 2rem 0;
}

.tab-selector {
    display: flex;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    padding: 0.25rem;
}

.tab-button {
    flex: 1;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-button.active {
    background: white;
    color: var(--gray-900);
    box-shadow: var(--shadow-sm);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.stat {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--gray-900);
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-top: 0.25rem;
}

/* Hero Visual */
.hero-visual {
    position: relative;
}

.hero-image-container {
    position: relative;
    background-color: transparent;
    border-radius: var(--radius-2xl);
    padding: 0;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image {
    width: 80%;
    max-width: 400px;
    height: auto;
    border-radius: var(--radius-xl);
}

.status-indicator {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-dot {
    width: 0.5rem;
    height: 0.5rem;
    background: var(--green-500);
    border-radius: 50%;
}

.delivery-status {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    background: white;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.delivery-icon {
    color: var(--green-500);
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.125rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Grid */
.features-grid {
    display: grid;
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--gray-300);
}

.feature-icon {
    width: 3rem;
    height: 3rem;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Steps Grid */
.steps-grid {
    display: grid;
    gap: 2rem;
}

.step {
    position: relative;
    text-align: center;
}

.step-connector {
    position: absolute;
    top: 2rem;
    left: 50%;
    width: 100%;
    height: 2px;
    background: var(--gray-200);
    z-index: 1;
}

.step:last-child .step-connector {
    display: none;
}

.step-content {
    position: relative;
    z-index: 2;
}

.step-number {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0 auto 1rem;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.step-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Trust Grid */
.trust-grid {
    display: grid;
    gap: 2rem;
}

.trust-item {
    text-align: center;
    padding: 2rem;
}

.trust-icon {
    width: 4rem;
    height: 4rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.trust-icon.blue {
    background: var(--blue-100);
    color: var(--blue-600);
}

.trust-icon.green {
    background: var(--green-100);
    color: var(--green-600);
}

.trust-icon.purple {
    background: var(--purple-100);
    color: var(--purple-600);
}

.trust-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.trust-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* CTA Section */
.cta-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1rem;
}

.cta-description {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 2rem;
}

.cta-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

/* Footer */
.footer-content {
    display: grid;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    color: white;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.logo-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.footer-description {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    display: block;
    padding: 0.25rem 0;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: white;
}

.footer-bottom {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.7);
}

.footer-legal {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-legal-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-legal-link:hover {
    color: white;
}

/* Homepage specific overrides and additions */
.homepage-hero {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 5rem;
}

.homepage-hero .hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.homepage-features {
    background: #ffffff;
    padding: 5rem 0;
}

.homepage-how-it-works {
    background: #f8fafc;
    padding: 5rem 0;
}

.homepage-trust {
    background: #ffffff;
    padding: 5rem 0;
}

.homepage-cta {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    padding: 5rem 0;
    color: white;
}

.homepage-footer {
    background: #0f172a;
    color: white;
    padding: 3rem 0 1rem 0;
}

/* Responsive adjustments for homepage */
@media (max-width: 768px) {
    .homepage-hero {
        padding-top: 4rem;
        min-height: auto;
        padding-bottom: 2rem;
    }
    
    .homepage-hero .hero-content {
        padding: 0 1rem;
    }
    
    .homepage-features,
    .homepage-how-it-works,
    .homepage-trust,
    .homepage-cta {
        padding: 3rem 0;
    }
}

/* Homepage specific animations */
.homepage-fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.homepage-fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.homepage-slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s ease-out;
}

.homepage-slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.homepage-slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s ease-out;
}

.homepage-slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Homepage button styles */
.homepage-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    border: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.homepage-btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.homepage-btn-secondary {
    background: transparent;
    color: #374151;
    padding: 0.75rem 1.5rem;
    border: 2px solid #d1d5db;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.homepage-btn-secondary:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    transform: translateY(-2px);
}

/* Homepage specific header styles */
.homepage-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    transition: all 0.3s ease;
}

.homepage-header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Homepage logo styles */
.homepage-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    text-decoration: none;
}

.homepage-logo-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Homepage navigation styles */
.homepage-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 1rem;
    padding-right: 1rem;
}

.homepage-nav-links {
    display: none;
    align-items: center;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.homepage-nav-links.desktop {
    display: flex;
}

.homepage-nav-link {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.homepage-nav-link:hover {
    color: #3b82f6;
}

.homepage-nav-actions {
    display: none;
    align-items: center;
    gap: 1rem;
}

.homepage-nav-actions.desktop {
    display: flex;
}

/* Mobile menu styles */
.homepage-mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.homepage-mobile-menu-toggle.desktop {
    display: none;
}

.homepage-mobile-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e5e7eb;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    padding: 1rem;
}

.homepage-mobile-menu.active {
    display: block;
}

.homepage-mobile-menu-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.homepage-mobile-menu-link {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.homepage-mobile-menu-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Responsive Design */
@media (min-width: 640px) {
    .action-buttons {
        flex-direction: row;
    }

    .cta-actions {
        flex-direction: row;
    }

    .footer-bottom {
        flex-direction: row;
        justify-content: space-between;
        text-align: left;
    }

    .footer-legal {
        flex-direction: row;
    }
}

@media (min-width: 768px) {
    .hero-stats {
        grid-template-columns: repeat(4, 1fr);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .trust-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .hero-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 4rem;
    }

    .hero-title {
        font-size: 4rem;
    }

    .section-title {
        font-size: 3rem;
    }

    .cta-title {
        font-size: 3rem;
    }

    .features-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .steps-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .step-connector {
        left: calc(100% - 2rem);
        width: calc(100% - 4rem);
    }

    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }

    .homepage-mobile-menu-toggle {
        display: none;
    }

    .homepage-nav-links,
    .homepage-nav-actions {
        display: flex;
    }
}

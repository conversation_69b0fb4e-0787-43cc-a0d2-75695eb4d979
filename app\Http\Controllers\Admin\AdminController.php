<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class AdminController extends Controller
{
    /**
     * Show admin dashboard.
     */
    public function dashboard()
    {
        $pendingUsers = User::where('status', 'pending')
            ->with('userDetail')
            ->orderBy('created_at', 'desc')
            ->get();

        $totalUsers = User::where('role', 'user')->count();
        $pendingCount = User::where('status', 'pending')->count();
        $approvedCount = User::where('status', 'approved')->count();
        $rejectedCount = User::where('status', 'rejected')->count();

        return view('admin.dashboard', compact(
            'pendingUsers',
            'totalUsers',
            'pendingCount',
            'approvedCount',
            'rejectedCount'
        ));
    }

    /**
     * Show user details for approval.
     */
    public function showUser($id)
    {
        $user = User::with('userDetail')->findOrFail($id);

        // Only show pending users or allow admins to view any user
        if (!Auth::user()->isAdmin() || ($user->status !== 'pending' && !request()->has('view'))) {
            abort(403);
        }

        return view('admin.user-details', compact('user'));
    }

    /**
     * Approve a user account.
     */
    public function approveUser(Request $request, $id)
    {
        $user = User::findOrFail($id);

        if ($user->status !== 'pending') {
            return back()->withErrors(['error' => 'Cet utilisateur n\'est pas en attente d\'approbation.']);
        }

        $user->update([
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => Auth::id(),
            'rejection_reason' => null,
        ]);

        // Update user detail verification status
        if ($user->userDetail) {
            $user->userDetail->update([
                'id_verification_status' => 'verified',
                'id_verified_at' => now(),
                'verified' => true,
            ]);
        }

        return redirect()->route('admin.dashboard')
            ->with('success', 'Utilisateur ' . $user->name . ' approuvé avec succès.');
    }

    /**
     * Reject a user account.
     */
    public function rejectUser(Request $request, $id)
    {
        $request->validate([
            'rejection_reason' => ['required', 'string', 'max:500'],
        ]);

        $user = User::findOrFail($id);

        if ($user->status !== 'pending') {
            return back()->withErrors(['error' => 'Cet utilisateur n\'est pas en attente d\'approbation.']);
        }

        $user->update([
            'status' => 'rejected',
            'rejection_reason' => $request->rejection_reason,
            'approved_by' => Auth::id(),
        ]);

        // Update user detail verification status
        if ($user->userDetail) {
            $user->userDetail->update([
                'id_verification_status' => 'rejected',
                'id_verification_notes' => $request->rejection_reason,
            ]);
        }

        return redirect()->route('admin.dashboard')
            ->with('success', 'Utilisateur ' . $user->name . ' rejeté.');
    }

    /**
     * Show all users (approved, rejected, suspended).
     */
    public function allUsers(Request $request)
    {
        $status = $request->get('status', 'all');
        $search = $request->get('search');

        $query = User::where('role', 'user')->with('userDetail');

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(20);

        return view('admin.all-users', compact('users', 'status', 'search'));
    }

    /**
     * Suspend a user account.
     */
    public function suspendUser($id)
    {
        $user = User::findOrFail($id);

        if ($user->isAdmin()) {
            return back()->withErrors(['error' => 'Impossible de suspendre un administrateur.']);
        }

        $user->update(['status' => 'suspended']);

        return back()->with('success', 'Utilisateur ' . $user->name . ' suspendu.');
    }

    /**
     * Reactivate a suspended user account.
     */
    public function reactivateUser($id)
    {
        $user = User::findOrFail($id);

        if ($user->status === 'suspended') {
            $user->update(['status' => 'approved']);
            return back()->with('success', 'Utilisateur ' . $user->name . ' réactivé.');
        }

        return back()->withErrors(['error' => 'Cet utilisateur n\'est pas suspendu.']);
    }
}

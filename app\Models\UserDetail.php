<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserDetail extends Model
{
    use HasUuids;

    protected $fillable = [
        'user_id',
        'birth_date',
        'gender',
        'address',
        'city',
        'cin',
        'profile_picture_url',
        'verified',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'verified' => 'boolean',
    ];

    /**
     * Get the user that owns the details.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}

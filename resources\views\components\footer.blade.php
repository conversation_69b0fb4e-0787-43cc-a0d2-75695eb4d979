{{-- Footer Component --}}
<footer class="footer">
    <div class="container">
        <div class="footer-content lg:grid-cols-4">
            {{-- Company Info --}}
            <div class="footer-section">
                <div class="footer-logo">
                    <div class="logo-icon">
                        <i data-lucide="truck" class="icon text-white"></i>
                    </div>
                    <span class="logo-text">CarryWay</span>
                </div>
                <p class="footer-description">
                    {{ $companyDescription ?? 'La plateforme de livraison collaborative qui connecte expéditeurs et transporteurs au Maroc. Sécurisé, fiable et efficace.' }}
                </p>
            </div>

            {{-- Solutions --}}
            <div class="footer-section">
                <h4 class="footer-title">Solutions</h4>
                <ul class="footer-links">
                    @php
                        $defaultSolutionLinks = [
                            ['url' => '#', 'text' => 'Pour expéditeurs'],
                            ['url' => '#', 'text' => 'Pour transporteurs'],
                            ['url' => '#', 'text' => 'Entreprises'],
                            ['url' => '#', 'text' => 'API & Intégrations']
                        ];
                        $solutionLinks = $solutionLinks ?? $defaultSolutionLinks;
                    @endphp
                    
                    @foreach($solutionLinks as $link)
                        <li><a href="{{ $link['url'] }}" class="footer-link">{{ $link['text'] }}</a></li>
                    @endforeach
                </ul>
            </div>

            {{-- Support --}}
            <div class="footer-section">
                <h4 class="footer-title">Support</h4>
                <ul class="footer-links">
                    @php
                        $defaultSupportLinks = [
                            ['url' => route('help') ?? '#', 'text' => 'Centre d\'aide'],
                            ['url' => route('contact') ?? '#', 'text' => 'Contact'],
                            ['url' => '#', 'text' => 'Signaler un problème'],
                            ['url' => '#', 'text' => 'Statut du service']
                        ];
                        $supportLinks = $supportLinks ?? $defaultSupportLinks;
                    @endphp
                    
                    @foreach($supportLinks as $link)
                        <li><a href="{{ $link['url'] }}" class="footer-link">{{ $link['text'] }}</a></li>
                    @endforeach
                </ul>
            </div>

            {{-- Company --}}
            <div class="footer-section">
                <h4 class="footer-title">Entreprise</h4>
                <ul class="footer-links">
                    @php
                        $defaultCompanyLinks = [
                            ['url' => route('about') ?? '#', 'text' => 'À propos'],
                            ['url' => '#', 'text' => 'Carrières'],
                            ['url' => '#', 'text' => 'Presse'],
                            ['url' => '#', 'text' => 'Partenaires']
                        ];
                        $companyLinks = $companyLinks ?? $defaultCompanyLinks;
                    @endphp
                    
                    @foreach($companyLinks as $link)
                        <li><a href="{{ $link['url'] }}" class="footer-link">{{ $link['text'] }}</a></li>
                    @endforeach
                </ul>
            </div>
        </div>

        <div class="footer-bottom md:flex-row">
            <p class="footer-copyright">
                © {{ date('Y') }} CarryWay. {{ $copyrightText ?? 'Tous droits réservés.' }}
            </p>
            <div class="footer-legal">
                @php
                    $defaultLegalLinks = [
                        ['url' => route('terms') ?? '#', 'text' => 'Conditions d\'utilisation'],
                        ['url' => route('privacy') ?? '#', 'text' => 'Politique de confidentialité'],
                        ['url' => '#', 'text' => 'Cookies']
                    ];
                    $legalLinks = $legalLinks ?? $defaultLegalLinks;
                @endphp
                
                @foreach($legalLinks as $link)
                    <a href="{{ $link['url'] }}" class="footer-legal-link">{{ $link['text'] }}</a>
                @endforeach
            </div>
        </div>
    </div>
</footer>

/* Modern CSS for CarryWay - Highly Designed Homepage */

/* CSS Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Futuristic Blue Color Palette */
    --primary: #0066ff;
    --primary-dark: #0052cc;
    --primary-light: #3385ff;
    --primary-ultra-light: #e6f2ff;
    --secondary: #00d4ff;
    --accent: #0099ff;
    --electric-blue: #00ccff;
    --cyber-blue: #0080ff;
    --neon-blue: #00b3ff;
    --success: #00ff88;
    --warning: #ffaa00;
    --danger: #ff3366;

    /* Futuristic Neutral Colors */
    --white: #ffffff;
    --ice-white: #f8fcff;
    --frost: #f0f8ff;
    --mist: #e8f4ff;
    --cloud: #d9ecff;
    --steel: #b3d9ff;
    --slate: #8cc8ff;
    --ocean: #66b3ff;
    --deep-blue: #1a365d;
    --midnight: #0f2a44;
    --void: #051f34;
    --abyss: #020d1a;

    /* Typography */
    --font-primary: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Figtree', sans-serif;

    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 120px;
    --section-padding-mobile: 80px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-base: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--deep-blue);
    background: linear-gradient(135deg, var(--ice-white) 0%, var(--frost) 50%, var(--mist) 100%);
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 102, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 153, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.2;
    color: var(--midnight);
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--electric-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.text-gradient::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--electric-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: blur(1px);
    opacity: 0.3;
    z-index: -1;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.875rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--cyber-blue) 50%, var(--secondary) 100%);
    color: var(--white);
    box-shadow:
        0 8px 32px rgba(0, 102, 255, 0.3),
        0 4px 16px rgba(0, 153, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(0, 153, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 12px 40px rgba(0, 102, 255, 0.4),
        0 6px 20px rgba(0, 153, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary);
    border: 2px solid var(--primary);
    box-shadow:
        0 4px 16px rgba(0, 102, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: var(--primary);
    color: var(--white);
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 8px 32px rgba(0, 102, 255, 0.3),
        0 4px 16px rgba(0, 153, 255, 0.2);
}

.btn-ghost {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border: 2px solid rgba(0, 204, 255, 0.3);
    backdrop-filter: blur(20px);
    box-shadow:
        0 4px 16px rgba(0, 153, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.btn-ghost:hover {
    background: rgba(0, 204, 255, 0.2);
    border-color: rgba(0, 204, 255, 0.5);
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 8px 32px rgba(0, 204, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Header/Navigation */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(248, 252, 255, 0.9);
    backdrop-filter: blur(30px);
    border-bottom: 1px solid rgba(0, 153, 255, 0.1);
    transition: all var(--transition-base);
    box-shadow: 0 4px 32px rgba(0, 102, 255, 0.05);
}

.header.scrolled {
    background: rgba(248, 252, 255, 0.95);
    box-shadow:
        0 8px 32px rgba(0, 102, 255, 0.1),
        0 4px 16px rgba(0, 153, 255, 0.05);
    border-bottom: 1px solid rgba(0, 153, 255, 0.2);
}

.nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--primary);
    text-decoration: none;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--electric-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    text-shadow: 0 0 30px rgba(0, 102, 255, 0.3);
}

.logo::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--electric-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: blur(2px);
    opacity: 0.3;
    z-index: -1;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
    list-style: none;
}

.nav-link {
    color: var(--deep-blue);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-base);
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
}

.nav-link:hover {
    color: var(--primary);
    background: rgba(0, 102, 255, 0.05);
    transform: translateY(-1px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
    transition: all var(--transition-base);
    transform: translateX(-50%);
    border-radius: 1px;
}

.nav-link:hover::after {
    width: 80%;
    box-shadow: 0 0 10px rgba(0, 102, 255, 0.5);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-700);
    cursor: pointer;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background:
        linear-gradient(135deg, var(--primary) 0%, var(--cyber-blue) 25%, var(--secondary) 50%, var(--electric-blue) 75%, var(--neon-blue) 100%);
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 204, 255, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 153, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 179, 255, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="%2300ccff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.4;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(0, 204, 255, 0.1) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(0, 153, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 8s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.3; transform: translateX(-10px); }
    50% { opacity: 0.6; transform: translateX(10px); }
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--white);
    margin-bottom: 1.5rem;
    line-height: 1.1;
    text-shadow:
        0 0 20px rgba(0, 204, 255, 0.5),
        0 0 40px rgba(0, 153, 255, 0.3),
        0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-text .highlight {
    color: var(--electric-blue);
    position: relative;
    text-shadow:
        0 0 30px rgba(0, 204, 255, 0.8),
        0 0 60px rgba(0, 179, 255, 0.4);
}

.hero-text .highlight::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--electric-blue) 0%, var(--secondary) 100%);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(0, 204, 255, 0.6);
}

.hero-text p {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 2.5rem;
    line-height: 1.6;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: var(--radius-2xl);
    box-shadow:
        0 25px 50px rgba(0, 102, 255, 0.3),
        0 15px 30px rgba(0, 153, 255, 0.2),
        0 5px 15px rgba(0, 204, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform var(--transition-slow);
    border: 1px solid rgba(0, 204, 255, 0.2);
    position: relative;
}

.hero-image::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, var(--electric-blue) 0%, var(--secondary) 50%, var(--neon-blue) 100%);
    border-radius: var(--radius-2xl);
    z-index: -1;
    opacity: 0.5;
    filter: blur(4px);
}

.hero-image:hover {
    transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.05);
    box-shadow:
        0 35px 70px rgba(0, 102, 255, 0.4),
        0 20px 40px rgba(0, 153, 255, 0.3),
        0 10px 20px rgba(0, 204, 255, 0.2);
}

/* Floating Elements */
.floating-element {
    position: absolute;
    animation: float 6s ease-in-out infinite;
}

.floating-element:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-element:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.floating-element:nth-child(3) {
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* Features Section */
.features {
    padding: var(--section-padding) 0;
    background:
        linear-gradient(135deg, var(--ice-white) 0%, var(--frost) 50%, var(--mist) 100%);
    position: relative;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 10% 20%, rgba(0, 153, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(0, 204, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.9);
    padding: 2.5rem;
    border-radius: var(--radius-xl);
    box-shadow:
        0 8px 32px rgba(0, 102, 255, 0.1),
        0 4px 16px rgba(0, 153, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 153, 255, 0.1);
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 50%, var(--electric-blue) 100%);
    transform: scaleX(0);
    transition: transform var(--transition-base);
    box-shadow: 0 0 10px rgba(0, 153, 255, 0.5);
}

.feature-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 50% 50%, rgba(0, 153, 255, 0.02) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover::after {
    opacity: 1;
}

.feature-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(0, 102, 255, 0.2),
        0 10px 30px rgba(0, 153, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    border-color: rgba(0, 153, 255, 0.2);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--cyber-blue) 50%, var(--secondary) 100%);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    color: var(--white);
    box-shadow:
        0 8px 32px rgba(0, 102, 255, 0.3),
        0 4px 16px rgba(0, 153, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
}

.feature-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--cyber-blue) 50%, var(--secondary) 100%);
    border-radius: var(--radius-xl);
    z-index: -1;
    filter: blur(4px);
    opacity: 0.6;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--gray-900);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* How It Works Section */
.how-it-works {
    padding: var(--section-padding) 0;
    background: var(--white);
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-top: 4rem;
}

.step {
    text-align: center;
    position: relative;
}

.step::after {
    content: '';
    position: absolute;
    top: 40px;
    right: -1.5rem;
    width: 3rem;
    height: 2px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--accent) 100%);
    display: none;
}

.step:not(:last-child)::after {
    display: block;
}

.step-number {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--white);
    box-shadow: var(--shadow-lg);
    position: relative;
    z-index: 2;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--gray-900);
}

.step-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* CTA Section */
.cta {
    padding: var(--section-padding) 0;
    background:
        linear-gradient(135deg, var(--midnight) 0%, var(--deep-blue) 50%, var(--void) 100%);
    color: var(--white);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(0, 153, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(0, 204, 255, 0.1) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse"><path d="M 40 0 L 0 0 0 40" fill="none" stroke="%2300ccff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.4;
}

.cta::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(0, 204, 255, 0.05) 50%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

.cta-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.cta-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--white);
    text-shadow:
        0 0 20px rgba(0, 204, 255, 0.5),
        0 0 40px rgba(0, 153, 255, 0.3);
    position: relative;
    z-index: 2;
}

.cta-description {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    opacity: 0.95;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 2;
}

.cta-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: 4rem 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--white);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: color var(--transition-base);
}

.footer-links a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: 2rem;
    text-align: center;
    color: var(--gray-400);
}

/* Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s ease-out;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s ease-out;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
    :root {
        --section-padding: 80px;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 3rem;
    }

    .steps-container .step::after {
        display: none;
    }
}

@media (max-width: 768px) {
    :root {
        --section-padding: 60px;
    }

    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-text p {
        font-size: 1.125rem;
    }

    .hero-actions {
        justify-content: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .cta-title {
        font-size: 2.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .steps-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}

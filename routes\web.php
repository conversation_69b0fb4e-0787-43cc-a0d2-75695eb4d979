<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;

// Page d'accueil
Route::get('/', [HomeController::class, 'index'])->name('home');

// Routes pour les annonces (à implémenter plus tard)
Route::prefix('announcements')->group(function () {
    // Annonces d'expéditeurs
    Route::get('/sender/create', function () {
        return view('announcements.sender.create');
    })->name('announcements.sender.create');

    // Annonces de voyageurs
    Route::get('/traveler/create', function () {
        return view('announcements.traveler.create');
    })->name('announcements.traveler.create');
});

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Additional routes for modern homepage components
Route::get('/dashboard', function () {
    return view('dashboard.index');
})->name('dashboard')->middleware('auth');

Route::get('/profile', function () {
    return view('profile.index');
})->name('profile')->middleware('auth');

Route::get('/settings', function () {
    return view('settings.index');
})->name('settings')->middleware('auth');

// Admin Routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/users/{id}', [AdminController::class, 'showUser'])->name('user.show');
    Route::post('/users/{id}/approve', [AdminController::class, 'approveUser'])->name('user.approve');
    Route::post('/users/{id}/reject', [AdminController::class, 'rejectUser'])->name('user.reject');
    Route::get('/users', [AdminController::class, 'allUsers'])->name('users.all');
    Route::post('/users/{id}/suspend', [AdminController::class, 'suspendUser'])->name('user.suspend');
    Route::post('/users/{id}/reactivate', [AdminController::class, 'reactivateUser'])->name('user.reactivate');
});

Route::get('/shipments/create', function () {
    return view('shipments.create');
})->name('shipments.create')->middleware('auth');

Route::get('/travels/create', function () {
    return view('travels.create');
})->name('travels.create')->middleware('auth');

Route::get('/help', function () {
    return view('help.index');
})->name('help');

Route::get('/contact', function () {
    return view('contact.index');
})->name('contact');

Route::get('/about', function () {
    return view('about.index');
})->name('about');

Route::get('/terms', function () {
    return view('legal.terms');
})->name('terms');

Route::get('/privacy', function () {
    return view('legal.privacy');
})->name('privacy');

// Routes du profil utilisateur (à implémenter plus tard)
Route::prefix('profile')->middleware('auth')->group(function () {
    Route::get('/', function () {
        return view('profile.index');
    })->name('profile.index');
});

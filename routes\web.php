<?php

use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Route;

// Page d'accueil
Route::get('/', [HomeController::class, 'index'])->name('home');

// Routes pour les annonces (à implémenter plus tard)
Route::prefix('announcements')->group(function () {
    // Annonces d'expéditeurs
    Route::get('/sender/create', function () {
        return view('announcements.sender.create');
    })->name('announcements.sender.create');

    // Annonces de voyageurs
    Route::get('/traveler/create', function () {
        return view('announcements.traveler.create');
    })->name('announcements.traveler.create');
});

// Routes d'authentification (à implémenter plus tard)
Route::prefix('auth')->group(function () {
    Route::get('/login', function () {
        return view('auth.login');
    })->name('login');

    Route::get('/register', function () {
        return view('auth.register');
    })->name('register');
});

// Additional routes for modern homepage components
Route::get('/dashboard', function () {
    return view('dashboard.index');
})->name('dashboard')->middleware('auth');

Route::get('/profile', function () {
    return view('profile.index');
})->name('profile')->middleware('auth');

Route::get('/settings', function () {
    return view('settings.index');
})->name('settings')->middleware('auth');

Route::post('/logout', function () {
    auth()->logout();
    return redirect()->route('home');
})->name('logout')->middleware('auth');

Route::get('/shipments/create', function () {
    return view('shipments.create');
})->name('shipments.create')->middleware('auth');

Route::get('/travels/create', function () {
    return view('travels.create');
})->name('travels.create')->middleware('auth');

Route::get('/help', function () {
    return view('help.index');
})->name('help');

Route::get('/contact', function () {
    return view('contact.index');
})->name('contact');

Route::get('/about', function () {
    return view('about.index');
})->name('about');

Route::get('/terms', function () {
    return view('legal.terms');
})->name('terms');

Route::get('/privacy', function () {
    return view('legal.privacy');
})->name('privacy');

// Routes du profil utilisateur (à implémenter plus tard)
Route::prefix('profile')->middleware('auth')->group(function () {
    Route::get('/', function () {
        return view('profile.index');
    })->name('profile.index');
});

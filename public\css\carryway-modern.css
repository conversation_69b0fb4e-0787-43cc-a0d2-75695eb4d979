/* CarryWay Modern Design - CSS Framework */
/* Replicating React/Tailwind design in pure CSS */

/* CSS Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern Color Palette - Gray Scale */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Accent Colors */
    --blue-100: #dbeafe;
    --blue-600: #2563eb;
    --green-100: #dcfce7;
    --green-500: #22c55e;
    --green-600: #16a34a;
    --purple-100: #f3e8ff;
    --purple-600: #9333ea;
    
    /* White */
    --white: #ffffff;
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    
    /* Spacing */
    --container-max-width: 1280px;
    --section-padding-y: 4rem;
    --section-padding-y-lg: 6rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-base: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-900);
    background: var(--white);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    color: var(--gray-900);
}

.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
}

.text-6xl {
    font-size: 3.75rem;
    line-height: 1;
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.font-bold {
    font-weight: 700;
}

.font-semibold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

.leading-tight {
    line-height: 1.25;
}

.leading-relaxed {
    line-height: 1.625;
}

/* Colors */
.text-gray-900 {
    color: var(--gray-900);
}

.text-gray-700 {
    color: var(--gray-700);
}

.text-gray-600 {
    color: var(--gray-600);
}

.text-gray-300 {
    color: var(--gray-300);
}

.text-white {
    color: var(--white);
}

.bg-white {
    background-color: var(--white);
}

.bg-gray-50 {
    background-color: var(--gray-50);
}

.bg-gray-100 {
    background-color: var(--gray-100);
}

.bg-gray-900 {
    background-color: var(--gray-900);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 2rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--gray-900);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--gray-800);
    transform: scale(1.02);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    border-color: var(--gray-400);
    color: var(--gray-900);
    transform: scale(1.02);
}

.btn-outline {
    background-color: transparent;
    color: var(--white);
    border: 1px solid var(--gray-600);
}

.btn-outline:hover {
    border-color: var(--gray-500);
    transform: scale(1.02);
}

/* Layout Utilities */
.flex {
    display: flex;
}

.inline-flex {
    display: inline-flex;
}

.grid {
    display: grid;
}

.hidden {
    display: none;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.space-x-2 > * + * {
    margin-left: 0.5rem;
}

.space-x-3 > * + * {
    margin-left: 0.75rem;
}

.space-x-4 > * + * {
    margin-left: 1rem;
}

.space-x-6 > * + * {
    margin-left: 1.5rem;
}

.space-x-8 > * + * {
    margin-left: 2rem;
}

.space-y-2 > * + * {
    margin-top: 0.5rem;
}

.space-y-4 > * + * {
    margin-top: 1rem;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.space-y-8 > * + * {
    margin-top: 2rem;
}

.gap-4 {
    gap: 1rem;
}

.gap-8 {
    gap: 2rem;
}

.gap-16 {
    gap: 4rem;
}

/* Grid */
.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
}

.md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

/* Spacing */
.p-1 {
    padding: 0.25rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-4 {
    padding: 1rem;
}

.p-6 {
    padding: 1.5rem;
}

.p-8 {
    padding: 2rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

.pt-8 {
    padding-top: 2rem;
}

.pt-24 {
    padding-top: 6rem;
}

.pb-16 {
    padding-bottom: 4rem;
}

.pb-24 {
    padding-bottom: 6rem;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.mb-12 {
    margin-bottom: 3rem;
}

.mb-16 {
    margin-bottom: 4rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.ml-2 {
    margin-left: 0.5rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

/* Positioning */
.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.top-0 {
    top: 0;
}

.left-0 {
    left: 0;
}

.right-0 {
    right: 0;
}

.bottom-0 {
    bottom: 0;
}

.top-4 {
    top: 1rem;
}

.right-4 {
    right: 1rem;
}

.bottom-4 {
    bottom: 1rem;
}

.left-4 {
    left: 1rem;
}

.top-6 {
    top: 1.5rem;
}

.left-full {
    left: 100%;
}

/* Z-index */
.z-50 {
    z-index: 50;
}

/* Width & Height */
.w-2 {
    width: 0.5rem;
}

.w-5 {
    width: 1.25rem;
}

.w-8 {
    width: 2rem;
}

.w-12 {
    width: 3rem;
}

.w-16 {
    width: 4rem;
}

.w-full {
    width: 100%;
}

.h-2 {
    height: 0.5rem;
}

.h-5 {
    height: 1.25rem;
}

.h-8 {
    height: 2rem;
}

.h-12 {
    height: 3rem;
}

.h-16 {
    height: 4rem;
}

.h-auto {
    height: auto;
}

.h-px {
    height: 1px;
}

.max-w-sm {
    max-width: 24rem;
}

.max-w-md {
    max-width: 28rem;
}

.max-w-2xl {
    max-width: 42rem;
}

.max-w-3xl {
    max-width: 48rem;
}

.max-w-7xl {
    max-width: 80rem;
}

/* Border */
.border {
    border-width: 1px;
}

.border-t {
    border-top-width: 1px;
}

.border-b {
    border-bottom-width: 1px;
}

.border-gray-100 {
    border-color: var(--gray-100);
}

.border-gray-200 {
    border-color: var(--gray-200);
}

.border-gray-300 {
    border-color: var(--gray-300);
}

.border-gray-600 {
    border-color: var(--gray-600);
}

/* Border Radius */
.rounded-full {
    border-radius: 9999px;
}

.rounded-lg {
    border-radius: var(--radius-lg);
}

.rounded-xl {
    border-radius: var(--radius-xl);
}

.rounded-2xl {
    border-radius: var(--radius-2xl);
}

/* Shadow */
.shadow-sm {
    box-shadow: var(--shadow-sm);
}

/* Text Alignment */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

/* Overflow */
.overflow-x-hidden {
    overflow-x: hidden;
}

/* Backdrop Filter */
.backdrop-blur-sm {
    backdrop-filter: blur(4px);
}

.backdrop-blur-xl {
    backdrop-filter: blur(24px);
}

/* Transitions */
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.transition-colors {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}

.duration-300 {
    transition-duration: 300ms;
}

/* Transform */
.scale-102:hover {
    transform: scale(1.02);
}

.translate-x-0\.5:hover {
    transform: translateX(0.125rem);
}

.-translate-y-1:hover {
    transform: translateY(-0.25rem);
}

.origin-left {
    transform-origin: left;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

.animate-fade-in {
    animation: fadeIn 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Responsive Design */
@media (min-width: 768px) {
    .md\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
    
    .md\:flex-row {
        flex-direction: row;
    }
    
    .md\:mb-0 {
        margin-bottom: 0;
    }
}

@media (min-width: 1024px) {
    .lg\:flex {
        display: flex;
    }
    
    .lg\:hidden {
        display: none;
    }
    
    .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    
    .lg\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
    
    .lg\:grid-cols-5 {
        grid-template-columns: repeat(5, minmax(0, 1fr));
    }
    
    .lg\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem;
    }
    
    .lg\:text-6xl {
        font-size: 3.75rem;
        line-height: 1;
    }
    
    .lg\:pt-32 {
        padding-top: 8rem;
    }
    
    .lg\:pb-24 {
        padding-bottom: 6rem;
    }
    
    .lg\:py-24 {
        padding-top: 6rem;
        padding-bottom: 6rem;
    }
    
    .lg\:col-span-2 {
        grid-column: span 2 / span 2;
    }
}

@media (min-width: 640px) {
    .sm\:flex-row {
        flex-direction: row;
    }
}

/* Flex Direction */
.flex-col {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-1 {
    flex: 1 1 0%;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

/* Component Styles */

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    transition: all 0.3s ease;
}

.header-scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(24px);
    border-bottom: 1px solid var(--gray-200);
}

.header-default {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
}

.nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    transition: transform 0.2s ease;
}

.logo:hover {
    transform: scale(1.02);
}

.logo-icon {
    width: 2rem;
    height: 2rem;
    background-color: var(--gray-900);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.nav-links {
    display: none;
    align-items: center;
    gap: 2rem;
    list-style: none;
}

.nav-link {
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.nav-link:hover {
    color: var(--gray-900);
    transform: translateY(-1px);
}

.nav-actions {
    display: none;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    padding: 0.5rem;
    color: var(--gray-600);
    cursor: pointer;
}

.mobile-menu {
    margin-top: 1rem;
    padding: 1rem 0;
    border-top: 1px solid var(--gray-100);
}

.mobile-menu-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.mobile-menu-link {
    color: var(--gray-600);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
}

.mobile-menu-link:hover {
    color: var(--gray-900);
}

.mobile-menu-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding-top: 1rem;
}

/* Hero Section */
.hero {
    padding-top: 6rem;
    padding-bottom: 4rem;
}

.hero-content {
    display: grid;
    gap: 4rem;
    align-items: center;
}

.hero-text {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
    width: fit-content;
}

.hero-title {
    font-size: 2.25rem;
    font-weight: 700;
    line-height: 1.25;
    color: var(--gray-900);
}

.hero-subtitle {
    color: var(--gray-600);
}

.hero-description {
    font-size: 1.25rem;
    color: var(--gray-600);
    line-height: 1.625;
    max-width: 42rem;
}

.hero-actions {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.tab-selector {
    display: flex;
    background-color: var(--gray-100);
    border-radius: var(--radius-lg);
    padding: 0.25rem;
    max-width: 24rem;
}

.tab-button {
    flex: 1;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: all 0.3s ease;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
}

.tab-button.active {
    background-color: var(--white);
    color: var(--gray-900);
    box-shadow: var(--shadow-sm);
}

.tab-button:not(.active) {
    color: var(--gray-600);
}

.tab-button:not(.active):hover {
    color: var(--gray-900);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-100);
}

.stat {
    text-align: left;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.hero-visual {
    position: relative;
}

.hero-image-container {
    position: relative;
    background-color: transparent;
    border-radius: var(--radius-2xl);
    padding: 0;
    border: none;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image {
    width: 80%;
    max-width: 400px;
    height: auto;
    border-radius: var(--radius-xl);
}

.status-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: var(--green-500);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    animation: float 3s ease-in-out infinite;
}

.status-dot {
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--white);
    border-radius: 50%;
}

.delivery-status {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: float 3s ease-in-out infinite;
    animation-delay: 1.5s;
}

.delivery-icon {
    color: var(--green-500);
}

.delivery-text {
    color: var(--gray-700);
}

/* Features Section */
.features {
    padding: 4rem 0;
    background-color: var(--gray-50);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.25rem;
    color: var(--gray-600);
    max-width: 48rem;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    gap: 2rem;
}

.feature-card {
    background-color: var(--white);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
}

.feature-card:hover {
    border-color: var(--gray-300);
}

.feature-icon {
    width: 3rem;
    height: 3rem;
    background-color: var(--gray-100);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.feature-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.feature-description {
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.625;
}

/* How It Works Section */
.how-it-works {
    padding: 4rem 0;
    background-color: var(--white);
}

.steps-grid {
    display: grid;
    gap: 2rem;
}

.step {
    position: relative;
}

.step-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.step-number {
    flex-shrink: 0;
    width: 3rem;
    height: 3rem;
    background-color: var(--gray-900);
    color: var(--white);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.step-text {
    flex: 1;
}

.step-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.step-description {
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.625;
}

.step-connector {
    position: absolute;
    top: 1.5rem;
    left: 100%;
    width: 100%;
    height: 1px;
    background-color: var(--gray-200);
    transform-origin: left;
    display: none;
}

/* Trust Section */
.trust {
    padding: 4rem 0;
    background-color: var(--gray-50);
}

.trust-grid {
    display: grid;
    gap: 2rem;
}

.trust-item {
    text-align: center;
}

.trust-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.trust-icon.blue {
    background-color: var(--blue-100);
    color: var(--blue-600);
}

.trust-icon.green {
    background-color: var(--green-100);
    color: var(--green-600);
}

.trust-icon.purple {
    background-color: var(--purple-100);
    color: var(--purple-600);
}

.trust-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.trust-description {
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* CTA Section */
.cta {
    padding: 4rem 0;
    background-color: var(--gray-900);
    color: var(--white);
    text-align: center;
}

.cta-content {
    max-width: 48rem;
    margin: 0 auto;
}

.cta-title {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.cta-description {
    font-size: 1.25rem;
    color: var(--gray-300);
    margin-bottom: 2rem;
}

.cta-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    justify-content: center;
}

/* Footer */
.footer {
    background-color: var(--white);
    border-top: 1px solid var(--gray-200);
    padding: 3rem 0;
}

.footer-content {
    display: grid;
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section {
    /* Base styles for footer sections */
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.footer-description {
    color: var(--gray-600);
    font-size: 0.875rem;
    line-height: 1.625;
    margin-bottom: 1rem;
    max-width: 28rem;
}

.footer-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.75rem;
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-link {
    color: var(--gray-600);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.footer-link:hover {
    color: var(--gray-900);
}

.footer-bottom {
    border-top: 1px solid var(--gray-200);
    padding-top: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
}

.footer-copyright {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.footer-legal {
    display: flex;
    gap: 1.5rem;
}

.footer-legal-link {
    color: var(--gray-600);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s ease;
}

.footer-legal-link:hover {
    color: var(--gray-900);
}

/* Responsive Design Overrides */
@media (min-width: 640px) {
    .action-buttons {
        flex-direction: row;
    }

    .cta-actions {
        flex-direction: row;
    }
}

@media (min-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .trust-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .footer-bottom {
        flex-direction: row;
    }

    .footer-copyright {
        margin-bottom: 0;
    }
}

@media (min-width: 1024px) {
    .nav-links {
        display: flex;
    }

    .nav-actions {
        display: flex;
    }

    .mobile-menu-toggle {
        display: none;
    }

    .hero {
        padding-top: 8rem;
        padding-bottom: 6rem;
    }

    .hero-content {
        grid-template-columns: 1fr 1fr;
    }

    .hero-title {
        font-size: 3.75rem;
        line-height: 1;
    }

    .section-title {
        font-size: 2.25rem;
    }

    .cta-title {
        font-size: 2.25rem;
    }

    .features {
        padding: 6rem 0;
    }

    .how-it-works {
        padding: 6rem 0;
    }

    .trust {
        padding: 6rem 0;
    }

    .cta {
        padding: 6rem 0;
    }

    .features-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .steps-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .step-connector {
        display: block;
    }

    .hero-stats {
        grid-template-columns: repeat(4, 1fr);
    }

    .footer-content {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
}

/* Animation Classes for JavaScript */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease-out;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease-out;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.95);
    transition: all 0.6s ease-out;
}

.scale-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* Icon Styles */
.icon {
    width: 1.5rem;
    height: 1.5rem;
    stroke: currentColor;
    fill: none;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.icon-sm {
    width: 1rem;
    height: 1rem;
}

.icon-lg {
    width: 2rem;
    height: 2rem;
}

/* Utility Classes */
.group:hover .group-hover\:translate-x-0\.5 {
    transform: translateX(0.125rem);
}

.cursor-pointer {
    cursor: pointer;
}

.select-none {
    user-select: none;
}

.pointer-events-none {
    pointer-events: none;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Focus States */
.btn:focus {
    outline: 2px solid var(--blue-600);
    outline-offset: 2px;
}

.tab-button:focus {
    outline: 2px solid var(--blue-600);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .header,
    .mobile-menu-toggle,
    .cta,
    .footer {
        display: none;
    }

    .hero {
        padding-top: 2rem;
    }

    * {
        background: white !important;
        color: black !important;
    }
}
